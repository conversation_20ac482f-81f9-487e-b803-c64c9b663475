import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useChat } from '@/hooks/useChat';
import { ChatFeatures, ChatTheme, ChatVariantConfig } from '@/components/chat';
import { ChatHeader } from './ChatHeader';
import ChatMessageList from './ChatMessageList';
import { ChatInput } from './ChatInput';
import { ChatSettings } from './ChatSettings';
import { ChatSidebar } from './ChatSidebar';

interface ChatContainerProps {
  userRole: 'admin' | 'provider' | 'customer';
  userId: string;
  recipientId?: string;
  chatId?: string;
  variant: 'compact' | 'full' | 'modal';
  features: ChatFeatures;
  theme: ChatTheme;
  variantConfig: ChatVariantConfig;
  externalSidebar?: boolean;
  hideHeader?: boolean; // Add this line
  onChatSelect: (chatId: string) => void;
  onMessageSent: (message: any) => void;
}

/**
 * ChatContainer Component
 * 
 * Main container that orchestrates all chat components and handles
 * navigation, state management, and role-specific rendering.
 */
const ChatContainer: React.FC<ChatContainerProps> = ({
  userRole,
  userId,
  recipientId,
  chatId: propChatId,
  variant,
  features,
  theme,
  variantConfig,
  externalSidebar = false,
  hideHeader = false, // Add this line
  onChatSelect,
  onMessageSent,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { chatId: routeChatId } = useParams<{ chatId?: string }>();
  const { state, loadChats, joinChat, leaveChat, connect } = useChat();

  // Check if we're on a messages page (to enable navigation)
  const isOnMessagesPage = location.pathname.includes('/messages');
  
  // Determine active chat ID from props or route
  const activeChatId = propChatId || routeChatId;
  
  // Local state for UI
  const [showSettings, setShowSettings] = useState(false);
  const [isMobileView, setIsMobileView] = useState(false);

  // Check for mobile view
  useEffect(() => {
    const checkMobile = () => {
      setIsMobileView(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Initialize chat connection and load chats
  useEffect(() => {
    connect();
    loadChats();
  }, [connect, loadChats]);

  // Handle chat selection from route or props
  useEffect(() => {
    if (activeChatId && activeChatId !== state.selectedChat?.id) {
      const chatExists = state.activeChats.some(chat => chat.id === activeChatId);

      if (chatExists) {
        joinChat(activeChatId);
        onChatSelect(activeChatId);
      } 
    }
  }, [activeChatId, state.selectedChat?.id, state.activeChats, state.isLoading, joinChat, onChatSelect]);

  useEffect(() => {
    // Only auto-select first chat on desktop or when not explicitly navigating back
    // Also check if external sidebar is enabled - don't auto-select when using external sidebar
    if (!state.selectedChat &&
        state.activeChats.length > 0 &&
        !activeChatId &&
        !state.isLoading &&
        !isMobileView &&
        !externalSidebar) { // Don't auto-select when using external sidebar
      const firstChat = state.activeChats[0];
      joinChat(firstChat.id);
      onChatSelect(firstChat.id);
    }
  }, [state.selectedChat, state.activeChats, activeChatId, state.isLoading, isMobileView, externalSidebar, joinChat, onChatSelect]);

  // Handle recipient-based chat creation
  useEffect(() => {
    if (recipientId && !activeChatId) {
      // Find existing chat with this recipient or create new one
      const existingChat = state.activeChats.find(chat => 
        chat.participants.some(p => p.id === recipientId)
      );
      
      if (existingChat) {
        handleChatSelect(existingChat.id);
      } else {
        // TODO: Create new chat with recipient
      }
    }
  }, [recipientId, activeChatId, state.activeChats]);

  // Handle chat selection - memoized to prevent unnecessary re-renders
  const handleChatSelect = useCallback((selectedChatId: string) => {
    if (selectedChatId !== state.selectedChat?.id) {
      // Leave current chat if any
      if (state.selectedChat) {
        leaveChat();
      }

      // Join new chat
      joinChat(selectedChatId);
      onChatSelect(selectedChatId);

      // Update URL for full variant - use messages route instead of chat
      if (variant === 'full' && isOnMessagesPage) {
        navigate(`/${userRole}/messages/${selectedChatId}`);
      }
    }
  }, [state.selectedChat?.id, leaveChat, joinChat, onChatSelect, variant, isOnMessagesPage, navigate, userRole]);

  // Handle new chat creation - memoized to prevent unnecessary re-renders
  const handleCreateNewChat = useCallback(() => {
    if (variant === 'full' && isOnMessagesPage) {
      navigate(`/${userRole}/messages/new`);
    }
    // TODO: Handle new chat creation for other variants
  }, [variant, isOnMessagesPage, navigate, userRole]);

  // Handle message sent - memoized to prevent unnecessary re-renders
  const handleMessageSent = useCallback((message: any) => {
    onMessageSent(message);
  }, [onMessageSent]);

  // Toggle settings panel
  const handleToggleSettings = () => {
    setShowSettings(!showSettings);
  };

  // Determine if header should be shown - combine hideHeader prop with variant config
  const shouldShowHeader = !hideHeader && variantConfig.showHeader;

  // Render based on variant
  const renderContent = () => {
    if (variant === 'compact') {
      return (
        <div className="flex flex-col h-full">
          {shouldShowHeader && (
            <ChatHeader
              chat={state.selectedChat}
              userRole={userRole}
              features={features}
              theme={theme}
              variant={variant}
              onToggleSettings={handleToggleSettings}
            />
          )}
          
          <div className="flex-1 flex flex-col min-h-0">
            <ChatMessageList
              messages={state.messages}
              currentUserId={userId}
              userRole={userRole}
              features={features}
              theme={theme}
              isLoading={state.isLoading}
            />
            
            <ChatInput
              chatId={state.selectedChat?.id}
              userRole={userRole}
              features={features}
              theme={theme}
              onMessageSent={handleMessageSent}
            />
          </div>
        </div>
      );
    }

    if (variant === 'modal') {
      return (
        <div className="flex flex-col h-full">
          <ChatHeader
            chat={state.selectedChat}
            userRole={userRole}
            features={features}
            theme={theme}
            variant={variant}
            onToggleSettings={handleToggleSettings}
            onClose={() => {
              // Handle modal close
              if (state.selectedChat) {
                leaveChat();
              }
            }}
          />
          
          <div className="flex-1 flex min-h-0">
            <div className="flex-1 flex flex-col">
              <ChatMessageList
                messages={state.messages}
                currentUserId={userId}
                userRole={userRole}
                features={features}
                theme={theme}
                isLoading={state.isLoading}
              />
              
              <ChatInput
                chatId={state.selectedChat?.id}
                userRole={userRole}
                features={features}
                theme={theme}
                onMessageSent={handleMessageSent}
              />
            </div>
          </div>
          
          {showSettings && (
            <ChatSettings
              chat={state.selectedChat}
              userRole={userRole}
              features={features}
              theme={theme}
              onClose={() => setShowSettings(false)}
            />
          )}
        </div>
      );
    }

    // Full variant - Mobile-first responsive layout
    return (
      <div className="flex h-full">
        {/* Mobile: Show sidebar OR chat content, not both */}
        {isMobileView ? (
          <>
            {/* Mobile: Show chat list when no chat selected, or chat content when chat selected */}
            {!state.selectedChat ? (
              // Only render ChatSidebar if not using external sidebar
              !externalSidebar && (
                <ChatSidebar
                  chats={state.activeChats}
                  selectedChatId={undefined}
                  userRole={userRole}
                  features={features}
                  theme={theme}
                  isLoading={state.isLoading}
                  onChatSelect={handleChatSelect}
                  onCreateNewChat={handleCreateNewChat}
                  isMobile={isMobileView}
                />
              )
            ) : (
              <div className="flex-1 flex flex-col min-w-0">
                {shouldShowHeader && (
                  <ChatHeader
                    chat={state.selectedChat}
                    userRole={userRole}
                    features={features}
                    theme={theme}
                    variant={variant}
                    onToggleSettings={handleToggleSettings}
                    onBack={() => {
                      // Clear selected chat to show message list
                      leaveChat();
                      // Navigate to messages page without chat ID to show full list
                      if (isOnMessagesPage) {
                        navigate(`/${userRole}/messages`, { replace: true });
                      }
                    }}
                  />
                )}

                <div className="flex-1 flex min-h-0">
                  <div className="flex-1 flex flex-col">
                    <ChatMessageList
                      messages={state.messages}
                      currentUserId={userId}
                      userRole={userRole}
                      features={features}
                      theme={theme}
                      isLoading={state.isLoading}
                    />

                    <ChatInput
                      chatId={state.selectedChat?.id}
                      userRole={userRole}
                      features={features}
                      theme={theme}
                      onMessageSent={handleMessageSent}
                    />
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          // Desktop: Show sidebar and chat side by side (only if not using external sidebar)
          <>
            {!externalSidebar && (
              <ChatSidebar
                chats={state.activeChats}
                selectedChatId={state.selectedChat?.id}
                userRole={userRole}
                features={features}
                theme={theme}
                isLoading={state.isLoading}
                onChatSelect={handleChatSelect}
                onCreateNewChat={handleCreateNewChat}
                isMobile={isMobileView}
              />
            )}

            <div className="flex-1 flex flex-col min-w-0">
              {shouldShowHeader && (
                <ChatHeader
                  chat={state.selectedChat}
                  userRole={userRole}
                  features={features}
                  theme={theme}
                  variant={variant}
                  onToggleSettings={handleToggleSettings}
                />
              )}

              <div className="flex-1 flex min-h-0">
                <div className="flex-1 flex flex-col">
                  <ChatMessageList
                    messages={state.messages}
                    currentUserId={userId}
                    userRole={userRole}
                    features={features}
                    theme={theme}
                    isLoading={state.isLoading}
                  />

                  <ChatInput
                    chatId={state.selectedChat?.id}
                    userRole={userRole}
                    features={features}
                    theme={theme}
                    onMessageSent={handleMessageSent}
                  />
                </div>

                {showSettings && (
                  <ChatSettings
                    chat={state.selectedChat}
                    userRole={userRole}
                    features={features}
                    theme={theme}
                    onClose={() => setShowSettings(false)}
                  />
                )}
              </div>
            </div>
          </>
        )}
      </div>
    );
  };

  return renderContent();
};

export default ChatContainer;